package com.ymx.app.controller.station;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ymx.app.BaseController;
import com.ymx.common.base.dao.IUserDao;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.mina.StringUtil;
import com.ymx.common.utils.DateUtils;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.photovoltaic.station.model.ComponentDayModel;
import com.ymx.service.photovoltaic.station.model.ComponentHourModel;
import com.ymx.service.photovoltaic.station.model.GroupPowerModel;
import com.ymx.service.photovoltaic.station.model.PowerStationModel;
import com.ymx.service.photovoltaic.station.service.ComponentDayService;
import com.ymx.service.photovoltaic.station.service.ComponentGroupService;

/**
 * 统计数据
 * @version 2018/10/18
 */
@RestController
@RequestMapping("statistics")
public class StatisticsAppController extends BaseController {
	@Resource
	private ComponentDayService componentDayService;
	@Resource
	private ComponentGroupService componentGroupService;
	@Resource
	private IUserDao userDao;

	private static final Logger logger = LoggerFactory.getLogger(StatisticsAppController.class);

	/**
	 * 统计  当前功率，当天电量，总电流 ，
	 * @return CallResult
	 */
	@RequestMapping(value="/queryComponentByStatistics.api")
	public CallResult queryComponentByStatistics(String powerStationId,String day,String language) {
		CallResult callResult = CallResult.newInstance();
		if (StringUtil.nullOrEmpty(day)) {
			// 时间为null 就获取现在的时间 day客户端默认不传
			day = LocalDate.now().toString();
		}

		try {
			PowerStationModel powerStationModel=componentDayService.queryPowerStationInfo(powerStationId);
			if(powerStationModel==null||powerStationModel.getCollectGap()==null)
			{
				getCallResult(callResult, language, ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT);
				return callResult;
			}

            logger.debug("powerStationId {} power_gap {}",powerStationId,powerStationModel.getCollectGap());
			//采集时间太短，采集器回复数据时间慢，计算时增大时间间隔
			if(powerStationModel.getCollectGap()==3)
			{
				powerStationModel.setCollectGap(12);
			}

			// 计算采集时间间隔 用60s/设置的间隔时间
            BigDecimal collect_gap=BigDecimal.valueOf(3600).divide(BigDecimal.valueOf(powerStationModel.getCollectGap()),2,RoundingMode.HALF_UP);
			logger.debug("powerStationId {} collect_gap {}",powerStationId,collect_gap);

			BigDecimal lastPower = new BigDecimal(0);
			BigDecimal totalPowerSum = new BigDecimal(0);
			BigDecimal todayKwh = new BigDecimal(0);
			BigDecimal allKwh = new BigDecimal(0);

			// 1. 计算dayKwh - 使用新的SQL查询
			String batchNo = day.replace("-", "");
			Map<String, Object> kwhMap = new HashMap<>();
			kwhMap.put("powerStationId", powerStationId);
			kwhMap.put("batch_no", batchNo);

			BigDecimal rawKwh = componentDayService.queryDayKwhFromComponentEnergy(kwhMap);
			if (rawKwh != null && powerStationModel.getKwhRate() != null) {
				todayKwh = rawKwh.multiply(powerStationModel.getKwhRate())
						.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
			} else {
				todayKwh = rawKwh != null ? rawKwh : BigDecimal.ZERO;
			}

			// 2. 计算powerSum - dayKwh乘以1000
			totalPowerSum = todayKwh.multiply(BigDecimal.valueOf(1000)).setScale(2, RoundingMode.HALF_UP);

			// 3. 计算power - 查询最近5分钟的采集数据
			String endTime = DateUtils.getNowTime();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String startTime = sdf.format(DateUtils.changeMinute(DateUtils.parse(endTime), -5));

			Map<String, Object> powerMap = new HashMap<>();
			powerMap.put("tableName", "_" + powerStationId);
			powerMap.put("startTime", startTime);
			powerMap.put("endTime", endTime);

			List<ComponentHourModel> recentCollectList = componentDayService.queryBatchNoCollectInfo(powerMap);
			if (!recentCollectList.isEmpty()) {
				ComponentHourModel lastRecord = recentCollectList.get(recentCollectList.size() - 1);
				if (lastRecord.getOutputCurrent() != null && lastRecord.getOutputVoltage() != null) {
					lastPower = lastRecord.getOutputCurrent().multiply(lastRecord.getOutputVoltage())
							.setScale(2, RoundingMode.HALF_UP);
				}
			}
			Map<String, Object> map = new HashMap<>();
			map.put("powerStationId",powerStationId);
			// 查询电站年发电量信息
			List<ComponentDayModel> yearModelList = componentDayService.queryComponentByYear(map);
			// 循环累加年发电量
			if (yearModelList != null && !yearModelList.isEmpty()) {
				allKwh = yearModelList.stream().map(ComponentDayModel::getKwh).reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// allKwh=电站所有年份发电量之和+dayKwh
			allKwh = allKwh.add(todayKwh).setScale(2, RoundingMode.HALF_UP);

			map.put("power", lastPower);
			map.put("powerSum", totalPowerSum);
			map.put("dayKwh", todayKwh);
			map.put("kwh", allKwh);
			callResult.setReModel(map);
		} catch (Exception e) {
			getCallResult(callResult, language, ErrCodeExt.SYSTEM_ERR);
			logger.error(e.getMessage());
		}
		return callResult;
	}


	/**
	 * 查询社会贡献数据
	 * @param userId 用户ID
	 * @param language 语言
	 * @return CallResult
	 */
	@RequestMapping(value="/queryContributionData.api")
	public CallResult queryContributionData(String userId, String language) {
		CallResult callResult = CallResult.newInstance();
		
		if (StringUtil.nullOrEmpty(userId)) {
			getCallResult(callResult, language, ErrCodeExt.PARAM_ERR);
			return callResult;
		}
		
		try {
			// 通过userId获取memberId
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("userId", userId);
			String memberId = userDao.queryUserLocal(paramMap);
			if (StringUtils.isEmpty(memberId)) {
				getCallResult(callResult, language, ErrCodeExt.PARAM_ERR);
				return callResult;
			}
			
			Map<String, Object> contributionData = componentDayService.calculateContribution(memberId);
			callResult.setReModel(contributionData);
		} catch (Exception e) {
			getCallResult(callResult, language, ErrCodeExt.SYSTEM_ERR);
			logger.error(e.getMessage(), e);
		}
		
		return callResult;
	}

	/**
	 * 查询历史发电量
	 * @param powerStationId 电站ID
	 * @param day 查询日期
	 * @param language 语言
	 * @return CallResult
	 */
	@RequestMapping(value="/queryHistoryKwh.api")
	public CallResult queryHistoryKwh(String powerStationId, String day, String language) {
		CallResult callResult = CallResult.newInstance();
		
		if (StringUtil.nullOrEmpty(powerStationId) || StringUtil.nullOrEmpty(day)) {
			getCallResult(callResult, language, ErrCodeExt.PARAM_ERR);
			return callResult;
		}
		
		try {
			// 转换日期格式
			String formattedDay = day.replace("-", "");
			BigDecimal historyKwh = componentDayService.queryHistoryKwh(powerStationId, formattedDay);
			Map<String, Object> result = new HashMap<>();
			result.put("kwh", historyKwh != null ? historyKwh.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			callResult.setReModel(result);
		} catch (Exception e) {
			getCallResult(callResult, language, ErrCodeExt.SYSTEM_ERR);
			logger.error(e.getMessage(), e);
		}
		
		return callResult;
	}

	/**
	 * 按分钟统计数据  不分组 取电站批次数据的集合
	 * @return CallResult
	 */
	@RequestMapping(value="/queryComponentByMinute.api")
	public CallResult queryComponentByMinute(String powerStationId,String day,String language) {
		CallResult callResult = CallResult.newInstance();
		try {
			List<ComponentHourModel> comHourModelList = getComHourModelList(powerStationId, day,false,null);
			Map<String, Object> map= handleComHourModelList(comHourModelList,null,null,"minute");
			callResult.setReModel(map);
		} catch (Exception e) {
			getCallResult(callResult, language, ErrCodeExt.SYSTEM_ERR);
			logger.error(e.getMessage());
		}
		return callResult;
	}

	/**
	 * 查询电站组串名称和id，支持按照组串名称模糊查询
	 * @return CallResult
	 */
	@RequestMapping(value="/queryGroup.api")
	public CallResult queryGroup(String powerStationId,String groupName,String language) {

		CallResult callResult = CallResult.newInstance();
		List<GroupPowerModel> groupModelList = componentGroupService.queryGroupIdAndName(powerStationId, groupName);
		Map<String, Object> map = new HashMap<>();
		map.put("data", groupModelList);
		callResult.setReModel(map);
		return callResult;
	}

	/**
	 * 按分钟统计数据  按组分
	 * @return CallResult
	 */
	@RequestMapping(value="/queryComponentByMinuteGroup.api")
	public CallResult queryComponentByMinuteGroup(String powerStationId,String day,String language,String groupName,
												  String groupId) {
		CallResult callResult = CallResult.newInstance();

		try {
			// 先去组串汇聚表取数据
			List<ComponentHourModel> comHourModelList = getComHourModelList(powerStationId, day, true, groupId);
			Map<String, Object> map= handleComHourModelList(comHourModelList,groupId,groupName,"minuteGroup");
			callResult.setReModel(map);
		} catch (Exception e) {
			getCallResult(callResult, language, ErrCodeExt.SYSTEM_ERR);
			logger.error(e.getMessage());
		}
		return callResult;
	}

	/**
	 *  处理comHourModelList 返回结果map
	 * @param comHourModelList 待处理的comHourModelList
	 * @param groupId 组串id
	 * @param groupName  组串名称
	 * @param handleType 处理类型
	 * @return 最终的结果map
	 */
	private Map<String, Object> handleComHourModelList(List<ComponentHourModel> comHourModelList,String groupId,
													   String groupName,String handleType) {

		String minDay = null;
		String maxDay = null;
		BigDecimal maxOutputCurrent = new BigDecimal(0);
		BigDecimal maxOutputVoltage = new BigDecimal(0);
		BigDecimal maxPower = new BigDecimal(0);

		List<GroupPowerModel> groupPowerModelList = new ArrayList<>();

		if (comHourModelList.size() > 0) {
			comHourModelList = comHourModelList.stream().
					filter(comHourModel -> comHourModel.getOutputCurrent() != null)
					.filter(comHourModel -> comHourModel.getOutputVoltage() != null)
					.collect(Collectors.toList());

			comHourModelList.forEach(comHourModel ->
			{
				// 设置createTimeCh和power的值
				comHourModel.setCreateTimeCh(getCreateTimeCh(comHourModel.getBatchNo()));
				BigDecimal power = comHourModel.getOutputCurrent().multiply(comHourModel.getOutputVoltage()).
						divide(BigDecimal.valueOf(1000.0), 2, RoundingMode.HALF_UP);
				comHourModel.setPower(power);
				comHourModel.setKwh(power.divide(BigDecimal.valueOf(60),2,RoundingMode.HALF_UP));
			});
			// 求最大输出电流 输出电压 输出功率
			maxOutputCurrent = comHourModelList.stream().map(ComponentHourModel::getOutputCurrent).max(BigDecimal::compareTo).
					orElse(BigDecimal.valueOf(0));
			maxOutputVoltage = comHourModelList.stream().map(ComponentHourModel::getOutputVoltage).max(BigDecimal::compareTo).
					orElse(BigDecimal.valueOf(0));
			maxPower = comHourModelList.stream().map(ComponentHourModel::getPower).max(BigDecimal::compareTo).
					orElse(BigDecimal.valueOf(0));
			minDay = comHourModelList.get(0).getCreateTimeCh();
			maxDay = comHourModelList.get(comHourModelList.size() - 1).getCreateTimeCh();

			if (handleType.equals("minuteGroup")) {
				GroupPowerModel groupPowerModel = new GroupPowerModel(groupId, groupName,
						maxPower, maxOutputCurrent, maxOutputVoltage);
				groupPowerModel.setList(comHourModelList);
				groupPowerModelList.add(groupPowerModel);
			}
		}

		Map<String, Object> map = new HashMap<>();
		map.put("minDay", minDay);
		map.put("maxDay", maxDay);
		if (handleType.equals("minuteGroup")) {
			map.put("data", groupPowerModelList);
		} else {
			map.put("outputCurrentMax", maxOutputCurrent);
			map.put("outputVoltageMax", maxOutputVoltage);
			map.put("powerMax", maxPower);
			map.put("data", comHourModelList);
		}
		return map;
	}


	/**
	 * 汇总天数据，返回月数据
	 * @return CallResult
	 */
	@RequestMapping(value="/queryComponentByDay.api")
	public CallResult queryComponentByDay(String powerStationId,String month,String language){
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map=getComDayModelList(powerStationId,month,"month");
		callResult.setReModel(map);
		return callResult;
	}
	/**
	 * 汇总月数据，返回年数据
	 * @return CallResult
	 */
	@RequestMapping(value="/queryComponentByMonth.api")
	public CallResult queryComponentByMonth(String powerStationId,String year,String language){
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map=getComDayModelList(powerStationId,year,"year");
		callResult.setReModel(map);
		return callResult;
	}
	/**
	 * 汇总年数据，返回所有数据
	 * @return CallResult
	 */
	@RequestMapping(value="/queryComponentByYear.api")
	public CallResult queryComponentByYear(String powerStationId,String language){
		CallResult callResult = CallResult.newInstance();
		Map<String, Object> map=getComDayModelList(powerStationId,null,"all");
		callResult.setReModel(map);
		return callResult;
	}

	private Map<String, Object> getComDayModelList(String powerStationId, String selectTime,
												   String queryType) {

		short minDay = 0;
		short maxDay = 0;
		BigDecimal maxKwh = new BigDecimal(0);
		Map<String, Object> map = new HashMap<>();
		map.put("powerStationId", powerStationId);
		List<ComponentDayModel> dayModelList = new ArrayList<>();
		switch (queryType) {
			case "month":
				map.put("selecttime", selectTime);
				dayModelList = componentDayService.queryComponentByDay(map);
				break;
			case "year":
				map.put("selecttime", selectTime);
				dayModelList = componentDayService.queryComponentByMonth(map);
				break;
			case "all":
				dayModelList = componentDayService.queryComponentByYear(map);
				break;
		}

		if (dayModelList.size() > 0) {
			dayModelList = dayModelList.stream().filter(comDayModel -> comDayModel.getKwh() != null).collect(Collectors.toList());
			switch (queryType) {
				case "month":
					dayModelList.forEach(comDayModel ->
					{
						// kwh保留2位小数 createTimeCh格式为dd 如01 createTime为date类型
						comDayModel.setKwh(comDayModel.getKwh().setScale(2, RoundingMode.HALF_UP));
						String day = comDayModel.getCreateTimeCh().split("-")[1];
						comDayModel.setCreateTimeCh(day);
						comDayModel.setCreateTime(DateUtils.parse(selectTime + "-" + day, DateUtils.DATE_SMALL_STR));
					});
					break;
				case "year":
					dayModelList.forEach(comDayModel ->
					{
						comDayModel.setKwh(comDayModel.getKwh().setScale(2, RoundingMode.HALF_UP));
						comDayModel.setCreateTime(DateUtils.parse(comDayModel.getCreateTimeCh(), "yyyy-MM"));
						String month = comDayModel.getCreateTimeCh().split("-")[1];
						comDayModel.setCreateTimeCh(month);
					});
					break;
				case "all":
					dayModelList.forEach(comDayModel ->
					{
						comDayModel.setKwh(comDayModel.getKwh().setScale(2, RoundingMode.HALF_UP));
						comDayModel.setCreateTime(DateUtils.parse(comDayModel.getCreateTimeCh(), "yyyy"));
					});
					break;
			}

			minDay = Short.parseShort(dayModelList.get(0).getCreateTimeCh());
			maxDay = Short.parseShort(dayModelList.get(dayModelList.size() - 1).getCreateTimeCh());
			maxKwh = dayModelList.stream().map(ComponentDayModel::getKwh).max(BigDecimal::compareTo).
					orElse(BigDecimal.valueOf(0));
		}
		map.clear();
		map.put("minDay", minDay);
		map.put("maxDay", maxDay);
		map.put("maxKwh", maxKwh);
		map.put("data", dayModelList);
		return map;
	}

	/**
	 * 查询电站或组串的批次电气数据
	 * @param powerStationId  电站id
	 * @param day  请求日期
	 * @param isQueryGroup  是否是组串查询
	 * @param groupId  组串id
	 * @return  电站或组串的批次电气数据
	 */
	private List<ComponentHourModel> getComHourModelList(String powerStationId, String day,
														 boolean isQueryGroup, String groupId) {
		Map<String, Object> map = new HashMap<>();
		map.put("tableName", "_" + powerStationId);
		day = day.replace("-", "");
		map.put("day", day);
		// 先去历史表中取数据
		List<ComponentHourModel> comHourModelList;
		if (isQueryGroup) {
			map.put("groupId",groupId);
			comHourModelList = componentDayService.queryDataByGroupId(map);
		} else {
			comHourModelList = componentDayService.queryDataGroupByBatchNo(map);
		}

		String today = LocalDate.now().toString();
		// 如果day 是今天 从采集表中取今天的数据
		if (day.trim().equals(today.replace("-", ""))) {
			//设置原始采集表查询的开始时间和结束时间
			int comListSize = comHourModelList.size();
			if (comListSize > 0) {
				// 组串汇聚表有数据 取最后一条的汇聚时间作为开始时间
				String lastGroupCollectTime = getCreateTimeCh(comHourModelList.get(comListSize - 1).getBatchNo());
				map.put("startTime", lastGroupCollectTime);
			} else {   // 没有数据 从5点开始查询
				map.put("startTime", today + " 05:00:00");
			}
			map.put("endTime", DateUtils.getNowTime());
			List<ComponentHourModel> todayComHourModelList;
			if (isQueryGroup) {
				todayComHourModelList = componentDayService.queryTodayGroupCollectInfo(map);
			} else {
				todayComHourModelList = componentDayService.queryTodayBatchNoCollectInfo(map);
			}
			comHourModelList.addAll(todayComHourModelList);
		}
		return comHourModelList;
	}


	private List<ComponentHourModel> getNewComHourModelList(String powerStationId, String day,
														 boolean isQueryGroup, String groupId,Integer power_gap) {
		Map<String, Object> map = new HashMap<>();
		map.put("tableName", "_" + powerStationId);
		day = day.replace("-", "");
		map.put("day", day);
		// 先去历史表中取数据
		List<ComponentHourModel> comHourModelList;
		if (isQueryGroup) {
			map.put("groupId",groupId);
			comHourModelList = componentDayService.queryDataByGroupId(map);
		} else {
			comHourModelList = componentDayService.queryGroupPowerByBatchNo(map);
		}

		String today = LocalDate.now().toString();
		// 如果day 是今天 从采集表中取今天的数据
		if (day.trim().equals(today.replace("-", ""))) {
			//设置原始采集表查询的开始时间和结束时间
			int comListSize = comHourModelList.size();
			if (comListSize > 0) {
				// 组串汇聚表有数据 取最后一条的汇聚时间作为开始时间
				String lastGroupCollectTime = getCreateTimeCh(comHourModelList.get(comListSize - 1).getBatchNo());
				map.put("startTime", lastGroupCollectTime);
			} else {   // 没有数据 从5点开始查询
				map.put("startTime", today + " 05:00:00");
			}
			map.put("endTime", DateUtils.getNowTime());
			List<ComponentHourModel> todayComHourModelList;
			if (isQueryGroup) {
				todayComHourModelList = componentDayService.queryTodayGroupCollectInfo(map);
			} else {
				todayComHourModelList = componentDayService.queryBatchNoCollectInfo(map);
			}
			comHourModelList.addAll(todayComHourModelList);
		}
		return comHourModelList;
	}


	/**
	 * 获取 createTimeCh
	 * @param batchNo 格式为yyyyMMddHHmmss
	 * @return  createTimeCh 格式为 yyyy-MM-dd HH:mm:ss
	 */
	private String getCreateTimeCh(String batchNo)
	{
		List<String> dayList=Arrays.asList(batchNo.substring(0,4),batchNo.substring(4,6),batchNo.substring(6,8));
		List<String> secondList=Arrays.asList(batchNo.substring(8,10),batchNo.substring(10,12),batchNo.substring(12));
		String dayStr=String.join("-",dayList);
		String secondStr=String.join(":",secondList);
		return String.join(" ",dayStr,secondStr);
	}


}
